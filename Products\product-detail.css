@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500&family=Playfair+Display:wght@400&family=Roboto:wght@600&display=swap" rel="stylesheet"');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Navigation, hamburger, cart */
.main-nav {
  height: 80px;
  background-color: #F9D8E2;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}
.hamburger-menu {
  position: absolute;
  left: 2rem;
  cursor: pointer;
}
.hamburger-icon {
  font-size: 1.5rem;
  z-index: 1001;
}
.menu-items {
  position: fixed;
  top: 0;
  left: -100%;
  width: 30%;
  height: 100vh;
  background-color: #F9D8E2;
  padding: 120px 2rem;
  transition: all 0.5s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.menu-items.active {
  left: 0;
}
.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}
.hamburger-menu:hover .menu-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
  backdrop-filter: blur(5px);
}
.menu-link {
  display: block;
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-decoration: none;
  font-family: 'Roboto', sans-serif;
  transition: transform 0.3s ease;
}
.menu-link:hover {
  transform: translateX(10px);
}
.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
.nav-logo {
    height: 80px;
}
.nav-center a {
    display: block;
    height: 80px;
}
.nav-center a:hover {
    opacity: 0.9;
    transition: opacity 0.3s ease;
}
.nav-right {
    position: absolute;
    right: 2rem;
}
.cart-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    border-bottom: 1px solid rgba(51, 51, 51, 0.2);
}
.cart-title {
    font-size: 1.5rem; /* Smaller text */
    font-family: 'Playfair Display', serif;
    color: #333;
    font-weight: 600;
    text-align: center;
}
.cart-icon-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}
.cart-icon {
    width: 24px;
    height: 24px;
    color: #333;
    transition: transform 0.2s ease;
}
.cart-icon:hover {
    transform: scale(1.1);
}
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #333;
    color: white;
    font-size: 12px;
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-family: 'Roboto', sans-serif;
}
.cart-panel {
    position: fixed;
    top: 0;
    right: -100%;
    width: 30%;
    height: 100vh;
    background: #F9D8E2;
    z-index: 1001;
    transition: right 0.5s ease;
}
.cart-panel.active {
    right: 0;
}
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    backdrop-filter: blur(5px);
}
.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}
.cart-header {
    border-bottom: 1px solid rgba(51, 51, 51, 0.2);
}
.cart-header h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    color: #333;
}
.cart-content {
    margin-top: 80px;
    padding: 2rem;
}
.empty-cart {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-family: 'Playfair Display', serif;
}
.empty-cart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
}
.empty-cart-icon {
    margin-bottom: 1.5rem;
}
.empty-cart-icon svg {
    width: 80px;
    height: 80px;
    color: #666;
}
.empty-cart-message {
    font-size: 1rem;
    color: #666;
    margin-bottom: 2rem;
    font-family: 'Roboto', sans-serif;
}
.start-shopping-btn {
    background-color: #333;
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Roboto', sans-serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}
.start-shopping-btn:hover {
    background-color: #444;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.close-cart {
    display: none;
}
.nav-right {
    position: absolute;
    right: 2rem;
}
.cart-icon-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}
.cart-icon {
    width: 24px;
    height: 24px;
    color: #333;
    transition: transform 0.2s ease;
}
.cart-icon:hover {
    transform: scale(1.1);
}
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #333;
    color: white;
    font-size: 12px;
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-family: 'Roboto', sans-serif;
}

/* Products */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #f4f4f4;
    --accent-color: #d4af37;
    --text-primary: #333333;
    --text-secondary: #666666;
    --background-light: #ffffff;
    --background-dark: #0a0a0a;
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 60px 20px;
}

.product-gallery {
    position: sticky;
    top: 20px;
    height: fit-content;
}

.main-image {
    width: 100%;
    margin-bottom: 20px;
}

.main-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.thumbnail-gallery {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.thumbnail {
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.thumbnail:hover, .thumbnail.active {
    opacity: 1;
}

.product-info {
    padding: 20px 0;
}

.product-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.product-price {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.product-description {
    line-height: 1.6;
    margin-bottom: 40px;
}

.size-selector {
    margin-bottom: 40px;
}

.size-selector h3 {
    margin-bottom: 15px;
}

.size-options {
    display: flex;
    gap: 10px;
}

.size-btn {
    width: 50px;
    height: 50px;
    border: 1px solid var(--text-secondary);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.size-btn:hover {
    border-color: var(--primary-color);
}

.size-btn.selected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.add-to-cart {
    width: 100%;
    padding: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    transition: background-color 0.3s ease;
    margin-bottom: 40px;
}

.add-to-cart:hover {
    background: var(--accent-color);
}

.product-details {
    border-top: 1px solid var(--text-secondary);
    padding-top: 40px;
}

.detail-section h3 {
    margin-bottom: 20px;
}

.detail-section ul {
    list-style: none;
    padding: 0;
}

.detail-section li {
    margin-bottom: 10px;
    color: var(--text-secondary);
}
.cart-items {
    padding: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(51, 51, 51, 0.2);
    position: relative;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 1rem;
}

.cart-item-details h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.remove-item {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.cart-total {
    padding: 1rem;
    text-align: right;
}

.checkout-btn {
    background: #333;
    color: white;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 25px;
    margin-top: 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.checkout-btn:hover {
    background: #444;
}
@media (max-width: 968px) {
    .product-detail {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .product-gallery {
        position: relative;
    }
}








/* Footer */
.footer {
    background-color: #F9D8E2;
    padding: 0.4rem 2rem;
    margin-top: auto;
    font-family: 'Roboto', sans-serif;
  }
  
  .social-list {
      display: flex;
      gap: 1.5rem;
      justify-content: center;
      list-style: none;
      margin: 1rem 0;
  }
  
  .social-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      transition: transform 0.15s ease;
  }
  
  .social-icon {
      width: 20px;
      height: 20px;
      color: #333;
  }
  
  .social-link:hover .social-icon-wrapper {
      transform: scale(1.2);
  }
  
  .footer-bottom {
      text-align: center;
  }
  
  .footer-copyright {
      margin-bottom: 0.7rem;
      font-family: 'Roboto', sans-serif;
  }
  .footer-policies {
      display: flex;
      justify-content: center;
      gap: 2.5rem;
      margin-top: 3rem;
      margin-bottom: 0;
      position: relative;
  }
  
  .footer-policies::before {
      content: '';
      position: absolute;
      top: -1.5rem;
      left: 10%;
      right: 10%;
      height: 1px;
      background-color: rgba(51, 51, 51, 0.2);
  }
  
  .footer-policies a {
      color: #333;
      text-decoration: none;
      font-size: 0.9rem;
      font-family: 'Roboto', sans-serif;
      transition: text-decoration 0.3s ease;
  }
  
  .footer-policies a:hover {
      text-decoration: underline;
  }