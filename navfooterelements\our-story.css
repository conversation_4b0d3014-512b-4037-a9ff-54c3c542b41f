@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500&family=Playfair+Display:wght@400&family=Roboto:wght@600&display=swap');

html {
    scroll-behavior: smooth;
}
.fade-in {
    opacity: 0;
    transform: translateY(40px);
    transition: opacity 1s ease-out, transform 1s ease-out;
}
.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    position: relative;
}
section {
    padding: 4rem 1.5rem;
}
main {
    flex: 1;
}


/* Navigation */
.main-nav {
    height: 80px;
    background-color: #F9D8E2;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}
.story-dialog {
    background-color: #fff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 2rem 0;
}

.dialog-container {
    max-width: 800px;
    margin: 0 auto;
}

.dialog-message {
    font-size: 1.6rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    border-radius: 8px;
    background: #fafafa;
    transition: transform 0.3s ease;
}

.dialog-message:hover {
    transform: translateX(10px);
}

.speaker {
    font-weight: bold;
    color: #F9D8E2;
    margin-right: 0.5rem;
}

.dialog-message em {
    color: #666;
    font-style: italic;
}

.highlight {
    background-color: #F9D8E2;
    color: #333;
}

.highlight .speaker {
    color: #333;
}


.hero-section {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(rgba(249, 216, 226, 0.2), rgba(249, 216, 226, 0.1));
    text-align: center;
}

.content-section {
    height: 100vh;
    overflow-y: auto;
}
.content-wrapper {
    background: white;
    position: relative;
    z-index: 2;
    min-height: 100%;

}
.hero-section h1 {
    font-size: 9rem;
    margin-bottom: 2rem;
    letter-spacing: 6px;
    color: #333;
    transform: translateY(-20px);
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
}

.hero-tagline {
    font-size: 2.8rem;
    font-family: 'Montserrat', sans-serif;
    color: #333;
    max-width: 900px;
    line-height: 1.4;
    opacity: 0;
    animation: fadeIn 1s ease 0.5s forwards;
}

.dialog-section {
    padding: 8rem 2rem;
    background: #fafafa;
}

.dialog-container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    padding: 4rem;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.08);
}

.dialog-message {
    font-size: 1.6rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    border-radius: 8px;
    background: #fafafa;
    transition: transform 0.3s ease;
}
.dialog-message.highlight {
    background: #F9D8E2;
}
.dialog-message:hover {
    transform: translateX(15px);
}

.dialog-conclusion {
    font-size: 2.2rem;
    color: #333;
    text-align: center;
    font-weight: 500;
    background: #F9D8E2;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: none;
}

.impact-section h2 {
    text-align: center;
    font-size: 3.5rem;
    margin-bottom: 2rem;
    margin-top: 2rem;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4rem;
    max-width: 1400px;
    margin: 4rem auto 0;
}

.impact-item {
    text-align: center;
    padding: 3rem;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.impact-item:hover {
    transform: translateY(-10px);
}

.impact-item h3 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.impact-item p {
    font-size: 1.4rem;
    line-height: 1.6;
    color: #666;
}

.call-to-action {
    background: #F9D8E2;
    padding: 8rem 2rem;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-top: 4rem;
}

.cta-button {
    font-size: 1.6rem;
    padding: 1.4rem 3.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: #333;
    color: white;
}

.cta-button.secondary {
    background: white;
    color: #333;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}


.mission-content {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 6rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    align-items: center;
}

.mission-text h2 {
    font-size: 3.5rem;
    margin-bottom: 2rem;
}

.mission-text p {
    font-size: 1.8rem;
    line-height: 1.8;
    color: #333;
    text-align: justify;
    height: fit-content;
}

.mission-section {
    min-height: 100vh;
    background: white;
}

.mission-image {
    height: 700px;
    overflow: hidden;
}

.mission-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.hamburger-menu {
  position: absolute;
  left: 2rem;
  cursor: pointer;
}


.hamburger-icon {
  font-size: 1.5rem;
  z-index: 1001;
}

.menu-items {
  position: fixed;
  top: 0;
  left: -100%;
  width: 30%;
  height: 100vh;
  background-color: #F9D8E2;
  padding: 120px 2rem;
  transition: all 0.5s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.menu-items.active {
  left: 0;
}

.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.hamburger-menu:hover .menu-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
  backdrop-filter: blur(5px);
}
.menu-link {
  display: block;
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-decoration: none;
  font-family: 'Roboto', sans-serif;
  transition: transform 0.3s ease;
}


.menu-link:hover {
  transform: translateX(10px);
}
.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-logo {
    height: 80px;
}


/* Footer */
.footer {
    background-color: #F9D8E2;
    padding: 0.4rem 2rem;
    width: 100%;
    position: relative;
    bottom: 0;
    font-family: 'Roboto', sans-serif;
}
.social-list {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    list-style: none;
    margin: 1rem 0;
}
.social-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    transition: transform 0.15s ease;
}
.social-icon {
    width: 20px;
    height: 20px;
    color: #333;
}
.social-link:hover .social-icon-wrapper {
    transform: scale(1.2);
}
.footer-bottom {
    text-align: center;
}
.footer-copyright {
    margin-bottom: 0.7rem;
    font-family: 'Roboto', sans-serif;
}
.footer-policies {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    margin-top: 3rem;
    margin-bottom: 0;
    position: relative;
}
.footer-policies::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: 10%;
    right: 10%;
    height: 1px;
    background-color: rgba(51, 51, 51, 0.2);
}
.footer-policies a {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-family: 'Roboto', sans-serif;
    transition: text-decoration 0.3s ease;
}
.footer-policies a:hover {
    text-decoration: underline;
}

/* Media Queries */

@media screen and (max-width: 1200px) {
    .menu-items {
        width: 40%;
    }
}
@media screen and (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    .products-container {
        padding: 3rem;
    }
    .menu-items {
        width: 42%;
    }
}
@media screen and (max-width: 992px) {
    .menu-items {
        width: 45%;
    }
}
@media screen and (max-width: 768px) {
    .main-nav {
        padding: 0 1rem;
    }
    
    .hamburger-menu {
        left: 1rem;
    }
    
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .products-container {
        padding: 2rem;
    }
    .menu-items {
        width: 55%;
    }
    .menu-link {
        font-size: 22px;
    }
    .hero-section h1 {
        font-size: 5rem;
    }
    
    .hero-tagline {
        font-size: 2rem;
    }
    
    .impact-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    .story-dialog {
        padding: 1rem;
    }
    
    .dialog-message {
        padding: 0.8rem;
    }
}
@media screen and (max-width: 576px) {
    .menu-items {
        width: 70%;
    }
    .menu-link {
        font-size: 20px;
    }
}
@media screen and (max-width: 480px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .products-container {
        padding: 1.5rem;
    }
    
    .product-name {
        font-size: 1rem;
    }
    
    .footer {
        padding: 2rem 1rem 0;
    }
    .menu-items {
        width: 65%;
    }
}
@media screen and (max-width: 375px) {
    .menu-items {
        width: 65%;
    }
    .menu-link {
        font-size: 18px;
    }
}
