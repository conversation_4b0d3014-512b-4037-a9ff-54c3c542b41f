/* =================================================================== */
/* === IMPORTS & RESET === */
/* =================================================================== */

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Cinzel:wght@400..900&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}


/* =================================================================== */
/* === NAVIGATION === */
/* =================================================================== */

/* Main Navigation Bar */
.main-nav {
  height: 90px;
  background-color: #F9D8E2;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}

.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-logo {
    height: 80px;
}

.nav-right {
    position: absolute;
    right: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Hamburger Menu */
.hamburger-menu {
  position: absolute;
  left: 2rem;
  cursor: pointer;
}

.hamburger-icon {
  font-size: 1.5rem;
  z-index: 1003;
}

.menu-items {
  position: fixed;
  top: 0;
  left: 0;
  transform: translateX(-100%);
  width: 450px;
  max-width: 80vw;
  height: 100vh;
  background-color: #F9D8E2;
  padding: 120px 2rem;
  transition: transform 0.4s ease;
  z-index: 1003;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.menu-items.active {
  transform: translateX(0);
}

.menu-link {
  display: block;
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-decoration: none;
  font-family: 'Roboto', sans-serif;
  transition: transform 0.3s ease;
}

.menu-link:hover {
  transform: translateX(10px);
}

/* Overlays */
.menu-overlay, 
.cart-overlay { 
    position: fixed; 
    top: 0; 
    left: 0; 
    width: 100%; 
    height: 100%; 
    background: rgba(25, 25, 25, 0.4); 
    opacity: 0; 
    visibility: hidden; 
    transition: opacity 0.4s ease; 
    z-index: 1000;
    backdrop-filter: blur(5px);
} 

.menu-overlay.active, 
.cart-overlay.active { 
    opacity: 1; 
    visibility: visible; 
}

.content-wrapper {
    transition: filter 0.4s ease;
}

.content-wrapper.blurred {
    filter: blur(6px);
    pointer-events: none;
}


/* =================================================================== */
/* === CART === */
/* =================================================================== */

/* Cart Icon */
.cart-icon-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon {
  width: 24px;
  height: 24px;
  color: #333;
  transition: transform 0.2s ease;
}

.cart-icon:hover {
  transform: scale(1.1);
}

/* Cart Panel */
.cart-panel {
    overflow: hidden;
    position: fixed;
    top: 0;
    right: -100%;
    width: 520px;
    max-width: 90vw;
    height: 98.2%;
    background: #ffffff;
    z-index: 1002;
    transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    flex-direction: column;
    box-shadow: -5px 0px 30px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin : 0.5%
}

.cart-panel.active {
    right: 0;
}

/* Cart Header */
.cart-header {
    height: 70px;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; 
}

.cart-title {
    font-size: 1.4rem;
    font-weight: 600;
    font-family: 'Amiri', sans-serif;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.cart-item-count {
    background-color: #e5e7eb;
    color: #374151;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.close-cart {
    background: none;
    border: none;
    font-size: 2rem;
    line-height: 1;
    cursor: pointer;
    color: #9ca3af;
    transition: color 0.2s;
}

.close-cart:hover {
    color: #111827;
}

/* Cart Banner */
.cart-banner {
    padding: 0.8rem 1rem;
    background-color: #ffffff;
    text-align: center;
    font-size: 0.85rem;
    font-family: 'Inter', sans-serif;
    border-bottom: 1px solid #e5e7eb;
}

.cart-banner p { 
    margin: 0; 
}

.progress-bar-container {
    width: 100%;
    background-color: #e5e7eb;
    border-radius: 99px;
    height: 6px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.progress-bar {
    width: 0%;
    height: 100%;
    background-color: #111827;
    border-radius: 99px;
    transition: width 0.4s ease;
}

/* Cart Content */
.cart-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

/* Cart Items */
.cart-item {
    display: grid;
    grid-template-columns: 80px 1fr;
    grid-template-rows: auto auto;
    gap: 0.5rem 1rem;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    font-family: 'Inter', sans-serif;
    align-items: center;
}

.cart-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.cart-item-image {
    width: 80px;
    height: 100px;
    object-fit: cover;
    border-radius: 6px;
    grid-row: 1 / span 2;
}

.cart-item-details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    grid-column: 2;
    grid-row: 1;
}

.cart-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.cart-item-name {
    font-weight: 600;
    line-height: 1.2;
    font-size: 1rem;
    margin-bottom: 10px;
    margin-top: 20px
}

.cart-item-size {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.cart-item-variant {
    font-size: 0.9rem;
    color: #6b7280;
}

.cart-item-price {
    font-size: 0.95rem;
    color: #374151;
    font-weight: 600;
    margin-top: auto;
}

/* Cart Item Actions */
.cart-item-actions {
    grid-column: 2;
    grid-row: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    align-items: flex-end;
    gap: 1rem;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    border: 9px solid #d1d5db;
    border-radius: 0px;
}

.quantity-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.4rem 0.6rem;
    font-size: 1rem;
    font-weight: 400;
    color:#f9f9f9
}

.quantity-btn:hover {
    background-color: #f9fafb;
}

.quantity {
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 25px;
    text-align: center;
    padding: 0 0.2rem;
}

.quantity-input {
    font-family: 'Inter', sans-serif;
    border: 1px solid rgba(209, 213, 219, 0.3);
    border-radius: 8px;
    width: 50px;
    height: 35px;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 400;
    background-color: white;
    color: #374151;
    -moz-appearance: textfield;
    appearance: textfield;
    transition: border-color 0.2s ease, background-color 0.2s ease;
}

.quantity-input:focus {
    outline: none;
    border-color: rgba(17, 24, 39, 0.3);
    background-color: rgba(255, 255, 255, 0.8);
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.remove-item {
    background: none;
    position: relative;
    border: none;
    font-size: 0.75rem;
    color: #9ca3af;
    text-decoration: none;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.remove-item:hover {
    color: #111827;
}

.remove-item::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 1px;
    background-color: #111827;
    transform: scaleX(0);
    transition: transform 0.2s ease;
    transform-origin: right;
}

.remove-item:hover::after {
    transform-origin: left;
    transform: scaleX(1);
}
    

/* Cart Footer */
.cart-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    background: #fff;
    flex-shrink: 0;
    font-family: 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    padding: 1.5rem;
    background-color: #f9fafb;
}

.cart-footer-note {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.cart-footer-note a {
    color: #6b7280;
    font-size: 0.9rem;
    text-decoration: underline;
}

.cart-summary .summary-line {
    display: flex;
    justify-content: space-between;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.cart-summary .summary-line span:last-child {
    font-weight: 700;
}

.cart-footer .summary-line {
    display: flex;
    justify-content: space-between;
    font-size: 1.1rem;
    font-weight: 500;
}

.cart-footer .total-price {
    font-weight: 700;
}

/* Notes */
.add-note-link, .edit-note-link {
    color: #6b7280;
    font-size: 0.9rem;
    text-decoration: underline;
    cursor: pointer;
}

.note-container .add-note-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.note-container .add-note-link:hover {
    color: #111827;
}

.note-container .add-note-link svg {
    transition: transform 0.2s ease-in-out;
}

.note-container .add-note-link:hover svg {
    transform: rotate(10deg);
}

.note-editor-container {
    text-align: left;
}

.note-editor-container h4 {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 0 1rem 0;
}

.note-textarea {
    width: 100%;
    min-height: 80px;
    padding: 0.75rem;
    box-sizing: border-box;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    resize: vertical;
    margin-bottom: 1rem;
}

.save-note-btn {
    display: inline-block;
    padding: 0.7rem 1.5rem;
    background-color: #111827;
    color: #ffffff;
    border: 1px solid #111827;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.save-note-btn:hover {
    opacity: 0.85;
}

.saved-note-container {
    text-align: left;
}

.saved-note-container h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #374151;
}

.saved-note-container p.saved-note-text {
    margin: 0 0 0.75rem 0;
    font-size: 0.95rem;
    color: #6b7280;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Checkout Button */
.checkout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.9rem;
    background-color: #111827;
    color: #ffffff;
    border: 1px solid #111827;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: transform 0.2s ease;
    font-size: 1rem;
    font-weight: 600;
    text-transform: none;
    transition: all 0.2s ease-in-out;
}

.checkout-btn:hover {
     background-color: transparent;
    color: #111827;
    transform: scale(1.02);
    background-color: #ffffff;
}

.icon-container {
    position: relative;
    width: 16px;
    height: 16px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
}

.lock-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transition: opacity 0.2s ease-in-out;
}

.lock-icon svg {
    width: 16px;
    height: 16px;
}

.checkout-btn .unlocked {
    opacity: 0;
    transform: scale(0.7);
}

.checkout-btn:hover .locked {
    opacity: 0;
    transform: scale(0.7);
}

.checkout-btn:hover .unlocked {
    opacity: 1;
    transform: scale(1);
}

.checkout-btn .lock-closed-img {
    filter: invert(1) brightness(2);
}

.checkout-btn:hover .lock-closed-img {
    opacity: 0;
}

.checkout-btn .lock-open-img {
    opacity: 0;
}

.checkout-btn:hover .lock-open-img {
    opacity: 1;
}

/* Empty Cart */
.empty-cart-container {
    text-align: center;
    padding: 4rem 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.empty-cart {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-cart-message {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.start-shopping-btn {
    background-color: #333;
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Roboto', sans-serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background-color: #111827;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: transform 0.2s ease;
}

.start-shopping-btn:hover {
    background-color: #444;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: scale(1.05);
}


/* =================================================================== */
/* === PRODUCTS === */
/* =================================================================== */

/* Shop Header */
.shop-header {
    padding: 150px 40px 80px;
    background: linear-gradient(to right, #f9f9f9, #ffffff);
    text-align: left;
    margin-bottom: 2rem;
}

.shop-banner {
    width: 100%;
    height: 500px;
    object-fit: cover;
    margin-bottom: 2rem;
}

.collection-text {
    font-family: 'cinzel', serif;
    font-weight: 500;
    font-size: 40px;
    background: transparent;
    text-align: left;
    margin-left: 25%;
    margin-top: 180px;
    margin-bottom: 80px;
    letter-spacing: 2px;
    color: #1a1a1a;
    text-shadow: rgba(255, 255, 255, 0.6) 1px 1px 1px, rgba(0, 0, 0, 0.6) -1px -1px 1px;
}

/* Products Container */
.products-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 5vw;
    margin-bottom: 100px;
}

.product-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.product-image-wrapper {
    align-self: center;
    aspect-ratio: 1/1;
    max-width: 400px;
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    aspect-ratio: 3/4;
    margin: 0 auto;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    width: 100%;
    justify-content: center;
}

.product-card {
    text-decoration: none;
    width: 100%;
    color: inherit;
    transition: all 0.3s ease;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: left;
}

.product-image-container {
    text-decoration: none;
    width: 100%;
    color: inherit;
    position: relative;
    display: block;
    border-radius: 16px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
    border-radius: 16px;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-image-container:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem 0;
    text-align: left;
    align-self: flex-start;
    text-align: left;
    width: 100%;
    margin: 0;
    transform: none;
}

.product-name {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
}

.product-price {
    font-family: 'Roboto', sans-serif;
    font-size: 1rem;
    color: #666;
    letter-spacing: 0.5px;
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #333;
    color: white;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
    z-index: 2;
}

/* Quick Shop */
.product-image-container:hover .quick-shop {
    bottom: 20px;
    opacity: 1;
}

.quick-shop {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 10px 20px;
    transition: all 0.3s ease;
    text-align: center;
    opacity: 0;
    width: auto;
    border-radius: 25px;
    z-index: 2;
    width: 80%;
    max-width: 200px;
}

.product-card:hover .quick-shop {
    bottom: 20px;
    opacity: 1;
}

.quick-add-btn {
    background: white;
    color: black;
    padding: 8px 16px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.size-selector-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 10px;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    border-radius: 25px;
}

.quick-add-btn:hover + .size-selector-overlay,
.size-selector-overlay:hover {
    opacity: 1;
    pointer-events: auto;
}

.size-options {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.size-option {
    width: 35px;
    height: 35px;
    border: 1px solid #333;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.size-option:hover {
    background: #333;
    color: white;
}


/* =================================================================== */
/* === FOOTER === */
/* =================================================================== */

.footer {
  background-color: #F9D8E2;
  padding: 0.4rem 2rem;
  margin-top: auto;
  font-family: 'Roboto', sans-serif;
}

.social-list {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    list-style: none;
    margin: 1rem 0;
}

.social-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    transition: transform 0.15s ease;
}

.social-icon {
    width: 20px;
    height: 20px;
    color: #333;
}

.social-link:hover .social-icon-wrapper {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
}


.footer-copyright {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.footer-copyright small {
    color: #666;
    font-size: 0.875rem;
}

.footer-policies {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.footer-policies::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: 10%;
    right: 10%;
    height: 1px;
    background-color: rgba(51, 51, 51, 0.2);
}

.footer-policies a {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    transition: text-decoration 0.3s ease;
}

.footer-policies a:hover {
    text-decoration: underline;
}


/* =================================================================== */
/* === MEDIA QUERIES === */
/* =================================================================== */

/* Large Screens */
@media (min-width: 1400px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1600px;
        margin: 0 auto;
    }
    .product-image-wrapper {
        max-width: 300px;
    }
}

/* 1300px and below */
@media screen and (max-width: 1300px) {
    .menu-items {
        width: 40%;
    }
    .cart-panel {
        width: 40%;
    }
    .products-container {
        padding: 3rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 40px;
        margin-top: 160px;
        margin-bottom: 25px;
    }
}

/* 1200px and below */
@media screen and (max-width: 1200px) {
    .menu-items {
        width: 40%;
    }
    .cart-panel {
        width: 40%;
    }
    .products-container {
        padding: 2rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 40px;
        margin-top: 160px;
        margin-bottom: 30px;
    }
}

/* 1024px and below */
@media screen and (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    .products-container {
        padding: 3rem;
    }
    .menu-items {
        width: 60%;
        max-width: 350px;
    }
    .cart-panel {
        width: 50%;
        max-width: 350px;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .menu-items {
        width: 50%;
    }
    .collection-text {
        font-size: 35px;
        margin-top: 160px;
        margin-bottom: 30px;
    }
}

/* 936px and below */
@media screen and (max-width: 936px) {
    .menu-items {
        width: 60%;
    }
    .product-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    .product-image-wrapper {
        max-width: 300px;
    }
    .products-container {
        padding: 2rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 32px;
        margin-top: 150px;
        margin-bottom: 30px;
    }
    .nav-logo {
        height: 75px;
    }
}

/* 768px and below */
@media screen and (max-width: 768px) {
    .product-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        justify-content: center;
    }
    .products-container {
        padding: 2rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 26px;
        margin-top: 140px;
        margin-bottom: 25px;
    }
    .menu-items {
        width: 50%;
    }
    .cart-panel {
        width: 80%;
    }
    .nav-logo {
        height: 70px;
    }
}

/* 576px and below */
@media screen and (max-width: 576px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        justify-content: center;
    }
    .products-container {
        padding: 2rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 20px;
        margin-top: 130px;
        margin-bottom: 25px;
    }
    .menu-items {
        width: 60%;
    }
    .cart-panel {
        width: 85%;
        max-width: 100vw;
    }
}

/* 480px and below */
@media screen and (max-width: 480px) {
    .cart-panel {
        width: 95%;
        max-width: 100vw;
    }
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        justify-content: center;
    }
    .products-container {
        padding: 2rem;
    }
    .product-card {
        max-width: 100%;
        text-align: center;
    }
    .product-card img {
        max-width: 100%;
        height: auto;
    }
    .product-info {
        width: 100%;
        padding: 1rem 0;
        transform: none;
        margin-left: 0;
    }
    .collection-text {
        font-size: 20px;
        margin-top: 120px;
        margin-bottom: 20px;
    }
    .product-name {
        font-size: 16px;
    }
    .menu-items {
        width: 65%;
    }
    .menu-link {
        font-size: 18px;
    }
    .nav-logo {
        height: 60px;
    }
}

/* 375px and below */
@media screen and (max-width: 375px) {
    .menu-items {
        width: 35%;
    }
    .menu-link {
        font-size: 18px;
    }
    .cart-panel {
        width: 95%;
    }
    .cart-title {
        font-size: 1.2rem;
    }
    .product-name {
        font-size: 14px;
    }
    .nav-logo {
        height: 55px;
    }
}

/* Additional Fixes */
.product-image-wrapper {
    width: 100%;
    max-width: 100%;
}

.quick-shop {
    width: 80%;
    max-width: 200px;
}

.size-options {
    justify-content: center;
}
