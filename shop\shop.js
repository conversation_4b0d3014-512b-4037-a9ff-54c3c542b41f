document.addEventListener('DOMContentLoaded', () => {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    let orderNote = localStorage.getItem('orderNote') || ''; // Load the note on start

    // --- Element Selectors ---
    const productItems = document.querySelectorAll('.product-item');
    const cartToggle = document.getElementById("cartToggle");
    const cartPanel = document.querySelector(".cart-panel");
    const cartOverlay = document.querySelector(".cart-overlay");
    const closeCartBtn = document.querySelector('.close-cart');
    const cartItemCount = document.querySelector('.cart-item-count');

    function openCart() {
        cartPanel.classList.add("active");
        cartOverlay.classList.add("active");
    }

    function closeCart() {
        cartPanel.classList.remove("active");
        cartOverlay.classList.remove("active");
    }

    if (cartToggle) cartToggle.addEventListener('click', openCart);
    if (closeCartBtn) closeCartBtn.addEventListener('click', closeCart);
    if (cartOverlay) cartOverlay.addEventListener('click', closeCart);

    productItems.forEach(item => {
        const productId = item.querySelector('.product-image-container').getAttribute('href').split('id=')[1];
        const quickShop = item.querySelector('.quick-shop');
        quickShop.innerHTML = `
            <button class="quick-add-btn">Quick Add</button>
            <div class="size-selector-overlay">
                <div class="size-options">
                    <button class="size-option" data-size="S">S</button>
                    <button class="size-option" data-size="M">M</button>
                    <button class="size-option" data-size="L">L</button>
                    <button class="size-option" data-size="XL">XL</button>
                </div>
            </div>`;
        
        const quickAddBtn = quickShop.querySelector('.quick-add-btn');
        const sizeSelector = quickShop.querySelector('.size-selector-overlay');
        const sizeOptions = quickShop.querySelectorAll('.size-option');
        
        quickAddBtn.addEventListener('mouseenter', () => { sizeSelector.style.display = 'block'; });
        sizeOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const size = option.dataset.size;
                addToCart(productId, size);
                sizeSelector.style.display = 'none';
                quickAddBtn.textContent = 'Added!';
                setTimeout(() => { quickAddBtn.textContent = 'Quick Add'; }, 1000);
            });
        });
        quickShop.addEventListener('mouseleave', () => { sizeSelector.style.display = 'none'; });
    });
    
    function addToCart(productId, size) {
        const product = getProductDetails(productId);
        if (!product) return;
        
        const existingItemIndex = cart.findIndex(item => item.id === productId && item.size === size);
        if (existingItemIndex > -1) {
            cart[existingItemIndex].quantity += 1;
        } else {
            cart.push({ ...product, size: size, quantity: 1 });
        }
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
    }
    
    // --- REPLACEMENT FOR getProductDetails ---
function getProductDetails(productId) {
    const productDatabase = {
        "reflective-knit": { id: "reflective-knit", name: "Reflective Knit", price: 200.00, image: "/assets/images/produits-test/2501-DVRL-REFLECTIVEKNIT-off_fond.jpg", category: "sweater", color: "Grey" },
        "blue-dragon-knit": { id: "blue-dragon-knit", name: "Blue Dragon Knit", price: 200.00, image: "/assets/images/produits-test/blue-dragon.jpg", category: "sweater", color: "Blue" },
        "black-shirt": { id: "black-shirt", name: "Black Shirt", price: 30.00, image: "/assets/images/produits-test/black-2_1.jpg", category: "shirt", color: "Black" },
    };
    return productDatabase[productId] || null;
}

    function updateFreeDeliveryBanner(cart, subtotal) {
        const bannerText = document.querySelector('.cart-banner p');
        const progressBar = document.querySelector('.cart-banner .progress-bar');
        if (!bannerText || !progressBar) return;

        const freeDeliveryThreshold = 35;
        let isEligible = false;
        let progressPercent = 0;
        const totalQuantity = cart.reduce((sum, item) => sum + item.quantity, 0);
        const containsOnlyShirts = cart.every(item => item.category === 'shirt');

        if (containsOnlyShirts && totalQuantity === 1) {
            isEligible = true;
        } else if (subtotal >= freeDeliveryThreshold) {
            isEligible = true;
        }

        if (isEligible) {
            bannerText.textContent = "Congratulations! You've got free delivery.";
            progressBar.style.width = '100%';
        } else {
            const remaining = freeDeliveryThreshold - subtotal;
            bannerText.innerHTML = `You're <strong>CHF ${remaining.toFixed(2)}</strong> away from free delivery.`;
            progressPercent = (subtotal / freeDeliveryThreshold) * 100;
            progressBar.style.width = `${progressPercent}%`;
        }
    }
    
    function renderNoteEditor() {
        const noteContainer = document.getElementById('note-section');
        if (!noteContainer) return;
        orderNote = localStorage.getItem('orderNote') || '';
        noteContainer.innerHTML = `
            <div class="note-editor-container">
                <h4>Order Note</h4>
                <textarea class="note-textarea" placeholder="Enter your note...">${orderNote}</textarea>
                <button class="save-note-btn">Save</button>
            </div>`;
    }

    function renderNoteSection() {
        const noteContainer = document.getElementById('note-section');
        if (!noteContainer) return;
        orderNote = localStorage.getItem('orderNote') || '';
        if (orderNote) {
            noteContainer.innerHTML = `
                <div class="saved-note-container">
                    <h4>Your Note:</h4>
                    <p class="saved-note-text">${orderNote}</p>
                    <a href="#" class="edit-note-link">Edit Note</a>
                </div>`;
        } else {
            // This version puts the pencil icon on the right
            noteContainer.innerHTML = `
                <a href="#" class="add-note-link">
                    <span>Add a note</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                </a>`;
        }
    }

    
function updateCartDisplay() {
    const cartContent = cartPanel.querySelector('.cart-content');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const cartTitle = document.querySelector('.cart-title');

    // Update cart title with English text
    if (cartTitle) {
        cartTitle.innerHTML = `Cart <span class="cart-item-count">${totalItems}</span>`;
    }

    if (cart.length === 0) {
        cartContent.innerHTML = `
            <div class="empty-cart-container">
                <p class="empty-cart">Your cart is empty</p>
                <a href="/shop/shop.html" class="start-shopping-btn">Start Shopping</a>
            </div>`;
        const cartFooter = cartPanel.querySelector('.cart-footer');
        const cartBanner = cartPanel.querySelector('.cart-banner');
        if (cartFooter) cartFooter.style.display = 'none';
        if (cartBanner) cartBanner.style.display = 'block';
        return;
    }

    const cartFooter = cartPanel.querySelector('.cart-footer');
    const cartBanner = cartPanel.querySelector('.cart-banner');
    if (cartFooter) cartFooter.style.display = 'flex';
    if (cartBanner) cartBanner.style.display = 'block';

    let cartItemsHTML = '';
    cart.forEach(item => {
        cartItemsHTML += `
            <div class="cart-item" data-id="${item.id}" data-size="${item.size}">
                <img src="${item.image}" alt="${item.name}" class="cart-item-image">
                <div class="cart-item-info">
                    <div class="cart-item-description">
                        <p class="cart-item-name">${item.name}</p>
                        <p class="cart-item-variant">${item.size} / ${item.color}</p>
                    </div>
                    <div class="cart-item-actions">
                        <input type="number" class="quantity-input" value="${item.quantity}" min="1">
                        <a href="#" class="remove-item">Remove</a>
                    </div>
                </div>
            </div>`;
    });
    cartContent.innerHTML = `<div class="cart-items-container">${cartItemsHTML}</div>`;

    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    updateFreeDeliveryBanner(cart, subtotal);

    // This is the updated footer HTML with the trust badge REMOVED.
    const cartFooterHTML = `
        <div class="summary-line">
            <span>Total</span>
            <span class="total-price">CHF ${subtotal.toFixed(2)}</span>
        </div>
        <div id="note-section" class="note-container">
            </div>
        <button class="checkout-btn">
             <span class="icon-container">
                <img src="/assets/svg locks/lock-svgrepo-com.svg" class="lock-icon lock-closed-img" alt="Lock">
                <img src="/assets/svg locks/unlock-svgrepo-com (1).svg" class="lock-icon lock-open-img" alt="Unlocked">
            </span>
            <span>PLACE MY ORDER</span>
        </button>`;
    
    let cartFooterContainer = cartPanel.querySelector('.cart-footer');
    if (!cartFooterContainer) {
        cartFooterContainer = document.createElement('div');
        cartFooterContainer.className = 'cart-footer';
        cartPanel.appendChild(cartFooterContainer);
    }
    cartFooterContainer.innerHTML = cartFooterHTML;
    renderNoteSection();
}

    // --- THIS IS THE FULLY MERGED AND CORRECTED EVENT LISTENER ---
cartPanel.addEventListener('input', (e) => {
    if (e.target.classList.contains('quantity-input')) {
        const cartItem = e.target.closest('.cart-item');
        if (cartItem) {
            const id = cartItem.dataset.id;
            const size = cartItem.dataset.size;
            const itemIndex = cart.findIndex(item => item.id === id && item.size === size);
            
            if (itemIndex > -1) {
                const newQuantity = parseInt(e.target.value, 10);
                if (newQuantity > 0) {
                    cart[itemIndex].quantity = newQuantity;
                    localStorage.setItem('cart', JSON.stringify(cart));
                    updateCartDisplay();
                } else if (newQuantity === 0) {
                    cart.splice(itemIndex, 1);
                    localStorage.setItem('cart', JSON.stringify(cart));
                    updateCartDisplay();
                }
            }
        }
    }
});
cartPanel.addEventListener('click', (e) => {
    // This smarter check finds the link even if you click the icon or text inside it
    if (e.target.closest('.add-note-link') || e.target.closest('.edit-note-link')) {
        e.preventDefault();
        renderNoteEditor();
    } else if (e.target.matches('.save-note-btn')) {
        e.preventDefault();
        const noteText = cartPanel.querySelector('.note-textarea').value;
        localStorage.setItem('orderNote', noteText);
        renderNoteSection();
    } else if (e.target.closest('.checkout-btn')) {
        e.preventDefault();
        
        // --- THIS IS THE FIX ---
        // This line tells the browser to go to your checkout page.
        window.location.href = '/checkout/checkout.html'; 
        
    } else if (e.target.closest('.remove-item')) {
        e.preventDefault();
        const cartItem = e.target.closest('.cart-item');
        if (cartItem) {
            const id = cartItem.dataset.id;
            const size = cartItem.dataset.size;
            const itemIndex = cart.findIndex(item => item.id === id && item.size === size);
            if (itemIndex > -1) {
                cart.splice(itemIndex, 1);
                localStorage.setItem('cart', JSON.stringify(cart));
                updateCartDisplay();
            }
        }
    }
});

    
    // Initial call to render the cart display correctly on page load
    updateCartDisplay();
});