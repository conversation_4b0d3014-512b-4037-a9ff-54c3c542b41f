<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - VIVEZ</title>
    <link rel="stylesheet" href="checkout.css">
    <link rel="stylesheet" href="/style.css">
    <!-- PayPal SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id=BAAmCpbSlR9fAtgUw9GZm83VZ5fRXW-QpU9btcUdOIjlzZ9joX8tHJkaLdmKPV33Znp4pLN74Q5LusuN7g&currency=CHF&enable-funding=card&disable-funding=venmo"></script>
</head>
<body>
    <nav class="main-nav">
        <div class="nav-center">
            <a href="/">
                <img src="/assets/images/logo/logo vivez 250603.png" alt="VIVEZ Logo" class="nav-logo">
            </a>
        </div>
    </nav>

    <div class="checkout-container">
        <main class="checkout-main">
            <div class="checkout-steps">
                <div class="step active">Information</div>
                <div class="step-divider"></div>
                <div class="step">Shipping</div>
                <div class="step-divider"></div>
                <div class="step">Payment</div>
            </div>

            <!-- Express Payment Options -->
            <section class="express-checkout">
                <h2 class="section-title">Express checkout</h2>
                <div class="express-payment-grid">
                    <button class="express-btn shop-pay-btn">
                        <span class="btn-text">Shop</span>
                        <span class="btn-highlight">Pay</span>
                    </button>
                    <div class="paypal-express" id="paypal-button-container"></div>
                </div>
            </section>

            <div class="separator"><span>OR</span></div>

            <form class="checkout-form" id="checkout-form">
                <section class="form-section">
                    <h2 class="section-title">Contact</h2>
                    <div class="field">
                        <input type="email" name="email" placeholder="Email" class="field__input" id="contact-email" required>
                    </div>
                    <div class="field__checkbox">
                        <input type="checkbox" id="newsletter">
                        <label for="newsletter">Email me with news and offers</label>
                    </div>
                </section>

                <section class="form-section">
                    <h2 class="section-title">Shipping address</h2>
                    <div class="field">
                        <select name="country" class="field__input" id="country" required>
                            <option value="Switzerland">Switzerland</option>
                            <option value="France">France</option>
                            <option value="Germany">Germany</option>
                            <option value="Italy">Italy</option>
                            <option value="Austria">Austria</option>
                        </select>
                    </div>
                    <div class="field--half">
                        <div class="field">
                            <input type="text" name="firstName" placeholder="First name" class="field__input" id="first-name" required>
                        </div>
                        <div class="field">
                            <input type="text" name="lastName" placeholder="Last name" class="field__input" id="last-name" required>
                        </div>
                    </div>
                    <div class="field">
                        <input type="text" name="address" placeholder="Address" class="field__input" id="address" required>
                    </div>
                    <div class="field">
                        <input type="text" name="apartment" placeholder="Apartment, suite, etc. (optional)" class="field__input" id="address2">
                    </div>
                    <div class="field--half">
                        <div class="field">
                            <input type="text" name="postalCode" placeholder="Postal code" class="field__input" id="postal-code" required>
                        </div>
                        <div class="field">
                            <input type="text" name="city" placeholder="City" class="field__input" id="city" required>
                        </div>
                    </div>
                </section>

                <section class="form-section">
                    <h2 class="section-title">Delivery method</h2>
                    <div class="delivery-options">
                        <label class="delivery-option">
                            <input type="radio" name="delivery" value="standard" checked>
                            <div class="delivery-option-content">
                                <div class="delivery-option-text">
                                    <span class="delivery-name">Standard delivery</span>
                                    <span class="delivery-time">2-4 business days</span>
                                </div>
                                <span class="delivery-price">Free</span>
                            </div>
                        </label>
                        <label class="delivery-option">
                            <input type="radio" name="delivery" value="express">
                            <div class="delivery-option-content">
                                <div class="delivery-option-text">
                                    <span class="delivery-name">Express delivery</span>
                                    <span class="delivery-time">1-2 business days</span>
                                </div>
                                <span class="delivery-price">CHF 9.90</span>
                            </div>
                        </label>
                    </div>
                </section>

                <section class="form-section">
                    <h2 class="section-title">Payment</h2>
                    <p class="section-subtitle">All transactions are secure and encrypted.</p>

                    <div class="payment-methods">
                        <div class="payment-method">
                            <label class="payment-option">
                                <input type="radio" name="payment" value="card" checked>
                                <div class="payment-content">
                                    <span class="payment-name">Credit card</span>
                                    <div class="payment-icons">
                                        <img src="/assets/images/visa.png" alt="Visa" class="payment-icon">
                                        <img src="/assets/images/mastercard.png" alt="Mastercard" class="payment-icon">
                                        <img src="/assets/images/amex.png" alt="American Express" class="payment-icon">
                                        <span class="more-options">+4</span>
                                    </div>
                                </div>
                            </label>

                            <div class="card-form" id="card-form">
                                <div class="field">
                                    <input type="text" name="cardNumber" placeholder="Card number" class="field__input" required>
                                </div>
                                <div class="field--half">
                                    <div class="field">
                                        <input type="text" name="expiry" placeholder="MM / YY" class="field__input" required>
                                    </div>
                                    <div class="field">
                                        <input type="text" name="cvv" placeholder="CVV" class="field__input" required>
                                    </div>
                                </div>
                                <div class="field">
                                    <input type="text" name="cardName" placeholder="Name on card" class="field__input" required>
                                </div>
                            </div>
                        </div>

                        <div class="payment-method">
                            <label class="payment-option">
                                <input type="radio" name="payment" value="paypal">
                                <div class="payment-content">
                                    <span class="payment-name">PayPal</span>
                                    <img src="/assets/images/paypal-logo.png" alt="PayPal" class="payment-logo">
                                </div>
                            </label>
                        </div>
                    </div>
                </section>

                <div class="form-footer">
                    <a href="/shop/shop.html" class="return-link">← Return to shop</a>
                    <button type="submit" class="continue-btn">Continue to payment</button>
                </div>
            </form>
        </main>

        <aside class="order-summary">
            <div class="order-summary-header">
                <h2 class="summary-title">Order summary</h2>
                <button class="summary-toggle">Show order summary</button>
            </div>
            <div class="order-summary-content">
                <div class="order-items" id="order-items">
                    <!-- Order items will be inserted here by JavaScript -->
                </div>
                <div class="promo-code-section">
                    <div class="promo-code-field">
                        <input type="text" placeholder="Discount code" class="field__input">
                        <button class="promo-apply-btn">Apply</button>
                    </div>
                </div>
                <div class="summary-totals">
                    <div class="summary-line">
                        <span>Subtotal</span>
                        <span id="summary-subtotal">CHF 0.00</span>
                    </div>
                    <div class="summary-line">
                        <span>Shipping</span>
                        <span id="shipping-cost">Free</span>
                    </div>
                    <div class="summary-line total">
                        <span>Total</span>
                        <span id="summary-total">CHF 0.00</span>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="/policies/privacy-policy" class="footer-link">Privacy policy</a>
                <a href="/policies/refund-policy" class="footer-link">Refund policy</a>
                <a href="/policies/shipping-policy" class="footer-link">Shipping policy</a>
                <a href="/policies/terms-of-service" class="footer-link">Terms of service</a>
            </div>
        </div>
    </footer>

    <script src="/Products/products.js"></script>
    <script src="/script.js"></script>
    <script src="/Checkout/checkout.js"></script>
</body>
</html>

