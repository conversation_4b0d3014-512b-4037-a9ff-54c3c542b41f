/* Payment Page Specific Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: #ffffff;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 2rem 2rem;
    flex: 1;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 400;
    color: #111827;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.page-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
    font-weight: 400;
}

.checkout-main {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 3rem;
    align-items: start;
}

.customer-info-summary {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e5e7eb;
    font-family: 'Inter', sans-serif;
}

.info-section {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.info-section:last-child {
    border-bottom: none;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-header span {
    font-weight: 600;
    color: #111827;
    font-family: 'Roboto', sans-serif;
}

.info-content {
    color: #374151;
    font-family: 'Inter', sans-serif;
    font-size: 0.95rem;
    line-height: 1.4;
}

.change-link {
    color: #111827;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.change-link:hover {
    background-color: #f3f4f6;
    text-decoration: underline;
}

.payment-section {
    margin-bottom: 2rem;
}

.section-title {
    font-family: 'Roboto', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1.5rem;
}

.express-payment {
    margin-bottom: 2rem;
}

.express-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
    font-family: 'Roboto', sans-serif;
    font-size: 1.1rem;
}

.express-buttons {
    margin-bottom: 1.5rem;
}

#paypal-button-container {
    min-height: 40px;
    width: 100%;
}

.payment-methods {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    background: #ffffff;
}

.payment-method {
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem;
    transition: background-color 0.2s ease;
}

.payment-method:last-child {
    border-bottom: none;
}

.payment-method:hover {
    background-color: #f9fafb;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
    color: #374151;
}

.payment-option input[type="radio"] {
    margin: 0;
    accent-color: #111827;
}

.payment-logo {
    height: 24px;
    margin-left: auto;
}

.card-form {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
    display: none;
}

.field-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.field {
    margin-bottom: 0.75rem;
}

.field__input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.field__input:focus {
    outline: none;
    border-color: #111827;
    box-shadow: 0 0 0 3px rgba(17, 24, 39, 0.1);
}

.checkout-steps {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.checkout-steps .step {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    transition: all 0.2s ease;
}

.checkout-steps .step.completed {
    background: #10b981;
    color: white;
}

.checkout-steps .step.active {
    background: #111827;
    color: white;
}

#complete-order-btn {
    width: 100%;
    padding: 1rem;
    background-color: #111827;
    color: #ffffff;
    border: 1px solid #111827;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: 'Roboto', sans-serif;
    letter-spacing: 0.5px;
    margin-top: 1rem;
}

#complete-order-btn:hover {
    background-color: transparent;
    color: #111827;
    transform: scale(1.02);
    background-color: #ffffff;
}

#twint-option {
    display: none;
}

.order-summary {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    position: sticky;
    top: 140px;
    height: fit-content;
}

.summary-title {
    font-family: 'Roboto', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.order-items {
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.order-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.item-image {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-quantity {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #111827;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #111827;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.item-size {
    font-size: 0.8rem;
    color: #6b7280;
}

.item-price {
    font-weight: 600;
    color: #111827;
    font-size: 0.9rem;
}

.order-totals {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.total-line.total {
    font-weight: 700;
    font-size: 1rem;
    color: #111827;
    border-top: 1px solid #e5e7eb;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .checkout-container {
        padding: 120px 1rem 2rem;
    }
    
    .checkout-main {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .order-summary {
        order: -1;
        position: static;
    }
    
    .checkout-steps {
        display: none;
    }
    
    .field-group {
        grid-template-columns: 1fr;
    }
    
    .customer-info-summary {
        padding: 1rem;
    }
    
    .info-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .change-link {
        align-self: flex-end;
    }
    
    .page-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .checkout-container {
        padding: 100px 0.5rem 1rem;
    }
    
    .payment-method {
        padding: 0.75rem;
    }
    
    .express-title {
        font-size: 1rem;
    }
    
    .page-title {
        font-size: 1.75rem;
    }
}
