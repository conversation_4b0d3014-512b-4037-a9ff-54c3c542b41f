document.addEventListener('DOMContentLoaded', function() {
    // Check if user came from checkout page
    const checkoutData = JSON.parse(localStorage.getItem('checkoutData'));
    if (!checkoutData) {
        alert('Please complete the checkout information first.');
        window.location.href = 'checkout.html';
        return;
    }

    displayCustomerInfo();
    displayOrderSummary();
    setupPaymentMethods();
    initializePayPal();
});

function displayCustomerInfo() {
    const checkoutData = JSON.parse(localStorage.getItem('checkoutData'));
    
    // Contact info
    document.getElementById('contact-info').textContent = checkoutData.email;
    
    // Shipping info
    const shippingInfo = `${checkoutData.firstName} ${checkoutData.lastName}, ${checkoutData.address}${checkoutData.apartment ? ', ' + checkoutData.apartment : ''}, ${checkoutData.city} ${checkoutData.postalCode}, ${checkoutData.country}`;
    document.getElementById('shipping-info').textContent = shippingInfo;
    
    // Delivery info
    const deliveryInfo = checkoutData.delivery === 'standard' ? 'Standard delivery (2-4 business days) - Free' : 'Express delivery (1-2 business days) - CHF 9.90';
    document.getElementById('delivery-info').textContent = deliveryInfo;
    
    // Show TWINT option for Switzerland
    if (checkoutData.country.toLowerCase() === 'switzerland') {
        document.getElementById('twint-option').style.display = 'block';
    }
}

function displayOrderSummary() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const checkoutData = JSON.parse(localStorage.getItem('checkoutData'));
    const orderItemsContainer = document.getElementById('order-items');
    
    let subtotal = 0;
    
    orderItemsContainer.innerHTML = '';
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;
        
        const orderItem = document.createElement('div');
        orderItem.className = 'order-item';
        orderItem.innerHTML = `
            <div class="item-image">
                <img src="${item.image}" alt="${item.name}">
                <span class="item-quantity">${item.quantity}</span>
            </div>
            <div class="item-details">
                <div class="item-name">${item.name}</div>
                ${item.size ? `<div class="item-size">Size: ${item.size}</div>` : ''}
            </div>
            <div class="item-price">CHF ${itemTotal.toFixed(2)}</div>
        `;
        orderItemsContainer.appendChild(orderItem);
    });
    
    // Calculate delivery cost
    const deliveryCost = checkoutData.delivery === 'express' ? 9.90 : 0;
    const total = subtotal + deliveryCost;
    
    // Update totals
    document.querySelector('.summary-subtotal').textContent = `CHF ${subtotal.toFixed(2)}`;
    document.querySelector('.delivery-cost').textContent = deliveryCost === 0 ? 'Free' : `CHF ${deliveryCost.toFixed(2)}`;
    document.querySelector('.summary-total').textContent = `CHF ${total.toFixed(2)}`;
}

function setupPaymentMethods() {
    const paymentOptions = document.querySelectorAll('input[name="payment"]');
    const cardForm = document.getElementById('card-form');
    const completeOrderBtn = document.getElementById('complete-order-btn');
    
    paymentOptions.forEach(option => {
        option.addEventListener('change', function() {
            if (this.value === 'card') {
                cardForm.style.display = 'block';
                completeOrderBtn.textContent = 'Complete order';
            } else {
                cardForm.style.display = 'none';
                completeOrderBtn.textContent = this.value === 'twint' ? 'Pay with TWINT' : 'Complete order';
            }
        });
    });
    
    completeOrderBtn.addEventListener('click', handlePayment);
}

function handlePayment() {
    const selectedPayment = document.querySelector('input[name="payment"]:checked').value;
    
    if (selectedPayment === 'card') {
        handleCreditCardPayment();
    } else if (selectedPayment === 'twint') {
        handleTwintPayment();
    }
}

function handleCreditCardPayment() {
    // Validate card form
    const cardNumber = document.querySelector('input[name="cardNumber"]').value;
    const expiry = document.querySelector('input[name="expiry"]').value;
    const cvv = document.querySelector('input[name="cvv"]').value;
    const cardName = document.querySelector('input[name="cardName"]').value;
    
    if (!cardNumber || !expiry || !cvv || !cardName) {
        alert('Please fill in all card details.');
        return;
    }
    
    // Simulate payment processing
    alert('🔄 Processing payment...\n\nNote: This is a demo. In production, you would integrate with Stripe or another payment processor.');
    
    // Simulate success
    setTimeout(() => {
        processSuccessfulPayment('CARD_' + Date.now());
    }, 2000);
}

function handleTwintPayment() {
    alert('🔄 Redirecting to TWINT...\n\nNote: This is a demo. In production, you would integrate with TWINT API.');
    
    // Simulate success
    setTimeout(() => {
        processSuccessfulPayment('TWINT_' + Date.now());
    }, 2000);
}

function initializePayPal() {
    console.log('🔄 Initializing PayPal...');
    
    if (typeof paypal === 'undefined') {
        console.error('❌ PayPal SDK not loaded');
        return;
    }

    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const checkoutData = JSON.parse(localStorage.getItem('checkoutData'));
    
    let subtotal = 0;
    const items = [];
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;
        
        items.push({
            name: item.name,
            unit_amount: {
                currency_code: 'CHF',
                value: item.price.toFixed(2)
            },
            quantity: item.quantity.toString(),
            description: item.size ? `Size: ${item.size}` : 'VIVEZ Product',
            sku: item.id || item.name.toLowerCase().replace(/\s+/g, '-')
        });
    });
    
    // Add delivery cost if express
    const deliveryCost = checkoutData.delivery === 'express' ? 9.90 : 0;
    if (deliveryCost > 0) {
        items.push({
            name: 'Express Delivery',
            unit_amount: {
                currency_code: 'CHF',
                value: deliveryCost.toFixed(2)
            },
            quantity: '1',
            description: '1-2 business days',
            sku: 'express-delivery'
        });
    }
    
    const total = subtotal + deliveryCost;

    paypal.Buttons({
        style: {
            layout: 'horizontal',
            color: 'gold',
            shape: 'rect',
            label: 'paypal',
            height: 40,
            tagline: false
        },
        
        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    reference_id: 'VIVEZ_ORDER_' + Date.now(),
                    description: 'VIVEZ Store Purchase',
                    amount: {
                        currency_code: 'CHF',
                        value: total.toFixed(2),
                        breakdown: {
                            item_total: {
                                currency_code: 'CHF',
                                value: total.toFixed(2)
                            }
                        }
                    },
                    items: items,
                    shipping: {
                        name: {
                            full_name: `${checkoutData.firstName} ${checkoutData.lastName}`
                        },
                        address: {
                            address_line_1: checkoutData.address,
                            address_line_2: checkoutData.apartment || '',
                            admin_area_2: checkoutData.city,
                            postal_code: checkoutData.postalCode,
                            country_code: getCountryCode(checkoutData.country)
                        }
                    }
                }],
                application_context: {
                    brand_name: 'VIVEZ',
                    landing_page: 'BILLING',
                    user_action: 'PAY_NOW'
                }
            });
        },
        
        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                processSuccessfulPayment(data.orderID);
            });
        },
        
        onError: function(err) {
            console.error('❌ PayPal Error:', err);
            alert('❌ Payment failed. Please try again.');
        },
        
        onCancel: function() {
            console.log('⚠️ Payment cancelled');
            alert('Payment was cancelled.');
        }
        
    }).render('#paypal-button-container');
}

function getCountryCode(country) {
    const countryCodes = {
        'switzerland': 'CH',
        'germany': 'DE',
        'france': 'FR',
        'italy': 'IT',
        'austria': 'AT'
    };
    return countryCodes[country.toLowerCase()] || 'CH';
}

function processSuccessfulPayment(transactionId) {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const checkoutData = JSON.parse(localStorage.getItem('checkoutData'));
    
    // Create order summary
    const orderSummary = cart.map(item => 
        `${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}`
    ).join('\n');
    
    alert(`🎉 Payment Successful!\n\nTransaction ID: ${transactionId}\n\nItems:\n${orderSummary}\n\nShipping to:\n${checkoutData.firstName} ${checkoutData.lastName}\n${checkoutData.address}\n${checkoutData.city}, ${checkoutData.postalCode}\n\nThank you for your purchase!`);
    
    // Clear cart and checkout data
    localStorage.removeItem('cart');
    localStorage.removeItem('checkoutData');
    
    // Redirect to shop
    window.location.href = '/shop/shop.html';
}
