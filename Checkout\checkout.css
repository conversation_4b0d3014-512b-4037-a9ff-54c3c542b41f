/* --- Base & Fonts --- */
:root {
    --primary-color: #F9D8E2;
    --text-color: #1a1a1a;
    --border-color: #e5e7eb;
    --light-bg: #f9fafb;
    --input-bg: #ffffff;
    --success-color: #10b981;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* --- Navbar --- */
.main-nav {
    height: 80px;
    background-color: #F9D8E2;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1000;
}

.nav-center {
    display: flex;
    justify-content: center;
}

.nav-logo {
    height: 70px;
}

/* --- Main Layout --- */
.checkout-container {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 80px); /* Account for navbar height */
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

@media (min-width: 992px) {
    .checkout-container {
        flex-direction: row;
        margin-top: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
    }
}

/* --- Main Content --- */
.checkout-main {
    flex: 1;
    padding: 1.5rem;
}

@media (min-width: 992px) {
    .checkout-main {
        width: 60%;
        padding: 2rem;
    }
}

/* --- Checkout Steps --- */
.checkout-steps {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.step {
    font-size: 0.9rem;
    color: #6b7280;
}

.step.active {
    color: var(--text-color);
    font-weight: 600;
}

.step-divider {
    flex: 1;
    height: 1px;
    background-color: var(--border-color);
    margin: 0 0.5rem;
}

/* --- Express Checkout --- */
.express-checkout {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.express-checkout .section-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: #111827;
}

.express-payment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.express-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.9rem;
    background-color: #111827;
    color: #ffffff;
    border: 1px solid #111827;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: transform 0.2s ease;
    font-size: 1rem;
    font-weight: 600;
    text-transform: none;
    transition: all 0.2s ease-in-out;
    font-family: 'Inter', sans-serif;
}

.express-btn:hover {
    background-color: transparent;
    color: #111827;
    transform: scale(1.02);
    background-color: #ffffff;
}

.icon-container {
    position: relative;
    width: 18px;
    height: 18px;
    margin-right: 8px;
}

.lock-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease-in-out;
}

.express-btn .lock-closed-img {
    filter: invert(1) brightness(2);
}

.express-btn:hover .lock-closed-img {
    opacity: 0;
}

.express-btn .lock-open-img {
    opacity: 0;
}

.express-btn:hover .lock-open-img {
    opacity: 1;
}

.paypal-express {
    border-radius: 6px;
    overflow: hidden;
}

#paypal-button-container {
    min-height: 44px;
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    color: #9ca3af;
    font-size: 0.8rem;
    margin: 1.5rem 0;
}

.separator::before,
.separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color);
}

.separator:not(:empty)::before {
    margin-right: 1em;
}

.separator:not(:empty)::after {
    margin-left: 1em;
}

/* --- Form Sections --- */
.form-section {
    margin-bottom: 2rem;
    background-color: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.section-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
    margin-top: -0.5rem;
}

.field {
    margin-bottom: 1rem;
}

.field__input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    background-color: var(--input-bg);
}

.field__input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px var(--primary-color);
}

.field--half {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.field__checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}
.auth-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f9fafb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-items: center;
    padding: 0.5rem 0;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-email {
    font-weight: 600;
    color: var(--text-color);
}

.user-status {
    font-size: 0.8rem;
    color: #6b7280;
}

.auth-options p {
    margin-bottom: 0.75rem;
    text-align: center;
}

.auth-options p:last-child {
    margin-bottom: 0;
}

.text-button {
    background: none;
    border: none;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

.text-button:hover {
    color: #111827;
    text-decoration: none;
}

/* Error state for form fields */
.field__input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px #ef4444;
}

/* Empty cart message */
.empty-cart-message {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 2rem 0;
}
#checkout-auth-container p {
    margin-bottom: 0.75rem; /* Space between "Sign In" and "Create Account" lines */
    text-align: center; /* Center the text */
}

#checkout-auth-container p:last-child {
    margin-bottom: 0;
}

.checkout-user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.text-button {
    background: none;
    border: none;
    color: var(--primary-color); /* Use your brand's primary color */
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem; /* Match surrounding text or set as desired */
    font-weight: 500;
    padding: 0.25rem 0.5rem; /* Add some padding for easier clicking */
}

.text-button:hover {
    color: #111827; /* Darken on hover */
    text-decoration: none; /* Optional: remove underline on hover */
}

/* --- Payment Methods --- */
.payment-methods {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
}

.payment-method {
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.payment-method:last-child {
    border-bottom: none;
}

.payment-method:hover {
    background-color: #f9fafb;
}

.payment-option {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    cursor: pointer;
    width: 100%;
}

.payment-option input[type="radio"] {
    margin: 0;
    margin-right: 0.75rem;
    accent-color: var(--primary-color);
}

.payment-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.payment-name {
    font-weight: 500;
    color: #111827;
}

.payment-icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.payment-icon {
    height: 24px;
    width: auto;
}

.payment-logo {
    height: 20px;
    width: auto;
}

.more-options {
    font-size: 0.75rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.card-form {
    padding: 1rem 1.25rem;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    display: block;
}

.card-form .field {
    margin-bottom: 0.75rem;
}

.card-form .field:last-child {
    margin-bottom: 0;
}

/* Clerk Modal Customization */
body .cl-modalRoot {
  z-index: 2000 !important;
}

body .cl-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

body .cl-formButtonPrimary {
  background-color: var(--primary-color) !important;
  color: var(--text-color) !important;
}

body .cl-formButtonPrimary:hover {
  background-color: #f0c5d3 !important;
}

body .cl-socialButtonsIconButton {
  border: 1px solid #e5e7eb !important;
}

body .cl-footerActionLink {
  color: #6b7280 !important;
}

body .cl-footerActionLink:hover {
  color: #111827 !important;
}


/* --- Delivery Options --- */
.delivery-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.delivery-option {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    cursor: pointer;
    transition: border-color 0.2s ease, background-color 0.2s ease;
}

.delivery-option:hover {
    background-color: #f9fafb;
}

.delivery-option input[type="radio"] {
    margin-right: 1rem;
}

.delivery-option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.delivery-option-text {
    display: flex;
    flex-direction: column;
}

.delivery-name {
    font-weight: 500;
}

.delivery-time {
    font-size: 0.8rem;
    color: #6b7280;
}

.delivery-price {
    font-weight: 600;
}

/* --- Form Footer --- */
.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.return-link {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.return-link:hover {
    color: #111827;
}

.continue-btn {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    font-size: 0.9rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.continue-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* --- Order Summary --- */
.order-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 2rem;
    border-top: 1px solid var(--border-color);
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
}

@media (min-width: 992px) {
    .order-summary {
        width: 50%;
        border-top: none;
        border-left: 1px solid var(--border-color);
        border-radius: 0 16px 16px 0;
        padding: 2.5rem;
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.08);
    }

    .checkout-main {
        grid-template-columns: 1fr 50%;
    }
}

.order-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.summary-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    letter-spacing: -0.025em;
}

.summary-toggle {
    display: none;
    background: none;
    border: none;
    color: #6b7280;
    font-size: 0.9rem;
    cursor: pointer;
}

@media (max-width: 991px) {
    .summary-toggle {
        display: block;
    }
    
    .order-summary-content {
        display: none;
    }
    
    .order-summary.expanded .order-summary-content {
        display: block;
    }
}

.order-items {
    margin-bottom: 1.5rem;
}

.summary-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:first-child {
    border-top: 1px solid var(--border-color);
}

.summary-item-image {
    position: relative;
}

.summary-item-image img {
    width: 64px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.summary-item-quantity {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #6b7280;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.summary-item-details {
    flex-grow: 1;
}

.summary-item-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.summary-item-variant {
    font-size: 0.8rem;
    color: #6b7280;
}

.summary-item-price {
    font-weight: 500;
}

/* --- Promo Code --- */
.promo-code-section {
    margin-bottom: 1.5rem;
}

.promo-code-field {
    display: flex;
    gap: 0.5rem;
}

.promo-apply-btn {
    padding: 0 1rem;
    background-color: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.2s ease;
}

.promo-apply-btn:hover {
    background-color: #f9fafb;
}

/* --- Summary Totals --- */
.summary-totals {
    font-size: 0.9rem;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.summary-line.total {
    font-size: 1.1rem;
    font-weight: 600;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    margin-top: 0.75rem;
}

/* --- Responsive Design --- */
@media (max-width: 1200px) {
    .checkout-container {
        max-width: 95%;
    }
}

@media (max-width: 991px) {
    .checkout-container {
        margin-top: 0;
        margin-bottom: 0;
        border-radius: 0;
    }
}

@media (max-width: 768px) {
    .nav-logo {
        height: 60px;
    }
    
    .checkout-main {
        padding: 1.5rem;
    }
    
    .form-section {
        padding: 1.25rem;
    }
}

@media (max-width: 576px) {
    .main-nav {
        height: 70px;
        padding: 0 1rem;
    }
    
    .nav-logo {
        height: 50px;
    }
    
    .checkout-main {
        padding: 1rem;
    }
    
    .form-section {
        padding: 1rem;
    }
    
    .field--half {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .form-footer {
        flex-direction: column-reverse;
        gap: 1rem;
        align-items: stretch;
    }
    
    .continue-btn {
        width: 100%;
    }
}

/* Authentication section styling */
.auth-section {
    margin-bottom: 2rem;
}

.checkout-user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.text-button {
    background: none;
    border: none;
    color: #6b7280;
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
}

.text-button:hover {
    color: #111827;
}

/* Clerk customization */
#checkout-sign-in .cl-card {
    box-shadow: none !important;
    padding: 0 !important;
}

#checkout-sign-in .cl-socialButtonsIconButton {
    border: 1px solid #e5e7eb !important;
}

#checkout-sign-in .cl-formButtonPrimary {
    background-color: var(--primary-color) !important;
    color: var(--text-color) !important;
}

#checkout-sign-in .cl-formButtonPrimary:hover {
    background-color: #f0c5d3 !important;
}

#checkout-sign-in .cl-footerActionLink {
    color: #6b7280 !important;
}

#checkout-sign-in .cl-footerActionLink:hover {
    color: #111827 !important;
}





/* 1. Main Page Layout & Typography
------------------------------------------- */
body {
    background-color: #f9fafb; /* Set a light grey background for the whole page */
}
.checkout-main {
    max-width: 680px; /* Optimal width for the main form column */
    margin: 0 auto;   /* Center the column on the page */
}
.page-header {
    text-align: left; /* Align title/subtitle left for a modern look */
    padding: 0;
    margin-bottom: 2rem;
}
.page-title {
    font-family: 'Inter', sans-serif;
    font-size: 1.75rem;
    font-weight: 600;
}
.page-subtitle {
    font-size: 1rem;
    margin-top: 0.25rem;
}


/* 2. Customer Information Summary Box
------------------------------------------- */
.customer-info-summary {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px; /* Softer rounded corners */
    box-shadow: 0 1px 3px rgba(0,0,0,0.03);
    margin-bottom: 3rem;
}
.info-section {
    display: grid; /* Use grid for better alignment */
    grid-template-columns: 80px 1fr auto;
    align-items: center;
    gap: 1.5rem;
    padding: 1.25rem 1.5rem;
}
.info-header {
    color: #6b7280;
    font-weight: 500;
}
.info-content {
    color: #111827;
    font-weight: 500;
    line-height: 1.5;
}
.change-link {
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: underline;
    color: #4b5563;
}
.change-link:hover {
    color: #000000;
}


/* 3. Secure & Interactive Payment Selection
------------------------------------------- */
.payment-section .section-title {
    font-size: 1.25rem;
}
.payment-methods {
    border: none;
    border-radius: 0;
    background: none;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.payment-method {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0;
    transition: all 0.2s ease-in-out;
}
/* Style for the currently selected payment option */
.payment-method.is-selected {
    border-color: #111827; /* Highlight with a black border */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
.payment-option {
    padding: 1.25rem;
    gap: 1rem;
    font-size: 1rem;
    font-weight: 600;
}
.payment-logo {
    height: 24px;
}


/* 4. Polished Credit Card Form
------------------------------------------- */
.card-form {
    padding: 0 1.25rem 1.25rem;
    margin: 0;
    border-top: 1px solid #e5e7eb;
}
.card-form .field__input {
    padding: 0.9rem;
    border-radius: 6px;
    font-size: 1rem;
    background-color: #f9fafb;
}


/* 5. Final "Complete Order" Button
------------------------------------------- */
.form-footer {
    /* The footer on the payment page looks best without the return link */
    justify-content: flex-end; /* Push button to the right */
}
.form-footer .return-link {
    display: none;
}
#complete-order-btn {
    padding: 1.25rem;
    font-size: 1rem;
    letter-spacing: 0.5px;
    text-transform: none; /* Softer look */
    border-radius: 12px;
}

/* --- Footer --- */


.footer {
  background-color: #F9D8E2;
  padding: 0.4rem 2rem;
  margin-top: auto;
  font-family: 'Roboto', sans-serif;
}

.social-list {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    list-style: none;
    margin: 1rem 0;
}

.social-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    transition: transform 0.15s ease;
}

.social-icon {
    width: 20px;
    height: 20px;
    color: #333;
}

.social-link:hover .social-icon-wrapper {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
}

.footer-copyright {
    margin-bottom: 0.7rem;
    font-family: 'Roboto', sans-serif;
}

.footer-policies {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    margin-top: 3rem;
    margin-bottom: 0;
    position: relative;
}

.footer-policies::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: 10%;
    right: 10%;
    height: 1px;
    background-color: rgba(51, 51, 51, 0.2);
}

.footer-policies a {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-family: 'Roboto', sans-serif;
    transition: text-decoration 0.3s ease;
}

.footer-policies a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .social-list {
        gap: 1rem;
    }

    .footer-policies {
        flex-direction: column;
        gap: 0.75rem;
    }
}