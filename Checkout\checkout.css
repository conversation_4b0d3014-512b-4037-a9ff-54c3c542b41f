:root {
    --primary-color: #F9D8E2; /* Your light pink */
    --primary-dark: #e6c2cf; /* A darker pink for hover states */
    --text-color: #1a1a1a;
    --border-color: #e5e7eb;
    --light-bg: #f9fafb;
    --input-bg: #ffffff;
}

body {
    font-family: 'Inter', -apple-system, sans-serif;
    color: var(--text-color);
    background-color: #fff;
    font-size: 16px;
    line-height: 1.5;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.page-container {
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.logo-container .nav-logo {
    height: 40px;
}

.breadcrumbs {
    margin-top: 1rem;
    font-size: 0.8rem;
    color: #6b7280;
}
.breadcrumbs a {
    color: inherit;
    text-decoration: none;
}
.breadcrumbs .current {
    font-weight: 500;
    color: var(--text-color);
}
.breadcrumbs span {
    margin: 0 0.5rem;
}

.checkout-grid {
    display: flex;
}

.main-content {
    flex: 1;
    padding: 2rem;
    border-right: 1px solid var(--border-color);
}

.order-summary {
    width: 45%;
    max-width: 500px;
    background-color: var(--light-bg);
    padding: 2rem;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.express-checkout {
    margin-bottom: 2rem;
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    color: #9ca3af;
    margin: 2rem 0;
}
.separator::before, .separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color);
}
.separator:not(:empty)::before { margin-right: 1em; }
.separator:not(:empty)::after { margin-left: 1em; }

#checkout-form section {
    margin-bottom: 2rem;
}

.shipping-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.shipping-option {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
}

.shipping-option input[type="radio"] {
    accent-color: var(--primary-dark);
}

.shipping-option.selected {
    border-color: var(--primary-dark);
    background-color: #fff;
    box-shadow: 0 0 0 1px var(--primary-dark);
}

.address-form .field__input, .address-form .field--half {
    margin-bottom: 1rem;
}

.field--half {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.field__input {
    width: 100%;
    padding: 0.9rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
}
.field__input:focus {
    outline: none;
    border-color: var(--primary-dark);
    box-shadow: 0 0 0 1px var(--primary-dark);
}

.form-footer {
    margin-top: 1.5rem;
}

.submit-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}
.submit-btn:hover {
    background-color: var(--primary-dark);
}

/* Order Summary Styles */
.order-summary-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.summary-item {
    display: flex;
    gap: 1rem;
    align-items: center;
}
.summary-item-image img {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: 8px;
}
.summary-item-details { flex-grow: 1; }
.summary-item-name { font-weight: 500; }
.summary-item-variant { font-size: 0.9rem; color: #6b7280; }
.summary-item-price { font-weight: 500; margin-left: auto; }

.promo-code-section {
    display: flex;
    gap: 0.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}
.promo-apply-btn {
    padding: 0 1rem;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
}
.summary-totals {
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}
.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}
.summary-line.total {
    font-weight: bold;
    font-size: 1.25rem;
}

/* PayPal Button Container */
#paypal-button-container {
    min-height: 50px;
}
/* =================================================================== */
/* === FINAL FIX & STYLE RESTORATION === */
/* =================================================================== */

/* 1. RESTORE THE NAVBAR
------------------------------------------- */
.main-nav {
    height: 80px;
    background-color: var(--primary-color); /* Your Brand Color */
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1000;
    position: static; /* Or 'fixed' if you want it to stick */
}
.nav-center {
    display: flex;
    justify-content: center;
}
.nav-logo {
    height: 70px;
}


/* 2. RESTORE THE ORDER SUMMARY STYLE
------------------------------------------- */
.order-summary {
    background-color: var(--light-bg); /* Your Light BG Color */
    border-left: 1px solid var(--border-color);
    padding: 2rem;
}
.summary-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}
.summary-item:first-child {
    border-top: 1px solid var(--border-color);
}
.summary-totals {
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
    padding-top: 1rem;
}


/* 3. RESTORE THE FOOTER BUTTONS LAYOUT
------------------------------------------- */
.form-footer {
    display: flex; /* This creates the side-by-side layout */
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}
.return-link {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}
.return-link:hover {
    color: #111827;
}

/* 4. RESTORE THE MAIN BUTTON STYLE YOU LIKED
------------------------------------------- */
.continue-btn {
    background-color: var(--primary-color); /* Your Brand Color */
    color: var(--text-color);
    border: none;
    border-radius: 6px;
    padding: 1rem 2rem;
    font-weight: 600;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
}
.continue-btn:hover {
    background-color: var(--primary-dark); /* Your Darker Brand Color */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}