document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Checkout page loaded');

    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    const orderItemsContainer = document.getElementById('order-items');
    const summarySubtotalElement = document.getElementById('summary-subtotal');
    const summaryTotalElement = document.getElementById('summary-total');

    function displayOrderSummary() {
        if (!orderItemsContainer) {
            console.error("Error: The '#order-items' container was not found.");
            return;
        }

        orderItemsContainer.innerHTML = '';

        if (cart.length === 0) {
            orderItemsContainer.innerHTML = '<p>Your cart is empty.</p>';
            if(summarySubtotalElement) summarySubtotalElement.textContent = 'CHF 0.00';
            if(summaryTotalElement) summaryTotalElement.textContent = 'CHF 0.00';
            return;
        }

        let subtotal = 0;
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            subtotal += itemTotal;
            const itemHTML = `
                <div class="summary-item">
                    <div class="summary-item-image">
                        <img src="${item.image}" alt="${item.name}">
                        <span class="summary-item-quantity">${item.quantity}</span>
                    </div>
                    <div class="summary-item-details">
                        <p class="summary-item-name">${item.name}</p>
                        ${item.size ? `<p class="summary-item-variant">Size: ${item.size}</p>` : ''}
                    </div>
                    <p class="summary-item-price">CHF ${itemTotal.toFixed(2)}</p>
                </div>`;
            orderItemsContainer.innerHTML += itemHTML;
        });

        if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
        if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
    }

    displayOrderSummary();

    // Handle form submission
    const checkoutForm = document.querySelector('.checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', handleFormSubmission);
    }

    // Update total when delivery option changes
    const deliveryOptions = document.querySelectorAll('input[name="delivery"]');
    deliveryOptions.forEach(option => {
        option.addEventListener('change', updateOrderTotal);
    });

    // Setup payment methods
    setupPaymentMethods();

    // Initialize PayPal
    initializePayPal();

    updateOrderTotal(); // Initial calculation
});

function updateOrderTotal() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    let subtotal = 0;

    cart.forEach(item => {
        subtotal += item.price * item.quantity;
    });

    // Get delivery cost
    const expressDelivery = document.querySelector('input[name="delivery"]:not(:checked)');
    const deliveryCost = expressDelivery && !expressDelivery.checked ? 0 : 9.90;

    const total = subtotal + deliveryCost;

    // Update display
    const summarySubtotalElement = document.querySelector('.summary-subtotal');
    const summaryTotalElement = document.querySelector('.summary-total');
    const deliveryElement = document.querySelector('.delivery-cost');

    if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
    if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${total.toFixed(2)}`;
    if(deliveryElement) deliveryElement.textContent = deliveryCost === 0 ? 'Free' : `CHF ${deliveryCost.toFixed(2)}`;
}

function handleFormSubmission(e) {
    e.preventDefault();

    // Validate required fields
    const requiredFields = [
        { selector: 'input[name="email"]', name: 'Email' },
        { selector: 'input[name="firstName"]', name: 'First name' },
        { selector: 'input[name="lastName"]', name: 'Last name' },
        { selector: 'input[name="address"]', name: 'Address' },
        { selector: 'input[name="city"]', name: 'City' },
        { selector: 'input[name="postalCode"]', name: 'Postal code' },
        { selector: 'select[name="country"]', name: 'Country' }
    ];

    let isValid = true;
    const errors = [];

    requiredFields.forEach(field => {
        const element = document.querySelector(field.selector);
        if (!element || !element.value.trim()) {
            isValid = false;
            errors.push(field.name);
            if (element) {
                element.style.borderColor = '#ff4444';
            }
        } else if (element) {
            element.style.borderColor = '';
        }
    });

    // Validate delivery option
    const deliverySelected = document.querySelector('input[name="delivery"]:checked');
    if (!deliverySelected) {
        isValid = false;
        errors.push('Delivery option');
    }

    // Check if payment method is selected
    const paymentSelected = document.querySelector('input[name="payment"]:checked');
    if (paymentSelected && paymentSelected.value === 'card') {
        // If credit card is selected, process payment directly
        if (!isValid) {
            alert(`Please fill in the following required fields:\n• ${errors.join('\n• ')}`);
            return;
        }

        // Save checkout data first
        saveCheckoutData();

        // Process credit card payment
        processCreditCardPayment();
        return;
    }

    if (!isValid) {
        alert(`Please fill in the following required fields:\n• ${errors.join('\n• ')}`);
        return;
    }

    // For other payment methods or no payment selected, redirect to payment page
    saveCheckoutData();
    window.location.href = 'payment.html';
}

function saveCheckoutData() {
    const formData = {
        email: document.querySelector('input[name="email"]').value,
        firstName: document.querySelector('input[name="firstName"]').value,
        lastName: document.querySelector('input[name="lastName"]').value,
        address: document.querySelector('input[name="address"]').value,
        apartment: document.querySelector('input[name="apartment"]').value,
        city: document.querySelector('input[name="city"]').value,
        postalCode: document.querySelector('input[name="postalCode"]').value,
        country: document.querySelector('select[name="country"]').value,
        delivery: document.querySelector('input[name="delivery"]:checked').value,
        timestamp: Date.now()
    };

    localStorage.setItem('checkoutData', JSON.stringify(formData));
    console.log('✅ Checkout data saved:', formData);
}

function initializePayPal() {
    console.log('🔄 Initializing PayPal...');

    // Check if PayPal SDK is loaded
    if (typeof paypal === 'undefined') {
        console.error('❌ PayPal SDK not loaded');
        document.getElementById('paypal-button-container').innerHTML =
            '<button class="express-btn paypal">PayPal (Loading...)</button>';
        return;
    }

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    console.log('🛒 Cart contents:', cart);

    // Calculate totals
    let subtotal = 0;
    const items = [];

    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;

        // Create PayPal item format
        items.push({
            name: item.name,
            unit_amount: {
                currency_code: 'CHF',
                value: item.price.toFixed(2)
            },
            quantity: item.quantity.toString(),
            description: item.size ? `Size: ${item.size}` : 'VIVEZ Product',
            sku: item.id || item.name.toLowerCase().replace(/\s+/g, '-')
        });
    });

    console.log('💰 Subtotal:', subtotal);
    console.log('📦 Items for PayPal:', items);

    // Only show PayPal if cart has items
    if (subtotal <= 0 || items.length === 0) {
        document.getElementById('paypal-button-container').innerHTML =
            '<button class="express-btn paypal" disabled>PayPal (Empty Cart)</button>';
        return;
    }

    // Render Advanced PayPal Button
    paypal.Buttons({
        style: {
            layout: 'horizontal',
            color: 'gold',
            shape: 'rect',
            label: 'paypal',
            height: 40,
            tagline: false
        },

        createOrder: function(data, actions) {
            console.log('🔄 Creating PayPal order with items:', items);

            return actions.order.create({
                purchase_units: [{
                    reference_id: 'VIVEZ_ORDER_' + Date.now(),
                    description: 'VIVEZ Store Purchase',
                    amount: {
                        currency_code: 'CHF',
                        value: subtotal.toFixed(2),
                        breakdown: {
                            item_total: {
                                currency_code: 'CHF',
                                value: subtotal.toFixed(2)
                            }
                        }
                    },
                    items: items
                }],
                application_context: {
                    brand_name: 'VIVEZ',
                    landing_page: 'BILLING',
                    user_action: 'PAY_NOW'
                }
            });
        },

        onApprove: function(data, actions) {
            console.log('✅ PayPal payment approved, Order ID:', data.orderID);

            return actions.order.capture().then(function(details) {
                console.log('✅ Payment captured:', details);

                // Show success message with order details
                const orderSummary = cart.map(item =>
                    `${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}`
                ).join('\n');

                alert(`🎉 Payment Successful!\n\nOrder ID: ${data.orderID}\nTotal: CHF ${subtotal.toFixed(2)}\n\nItems:\n${orderSummary}\n\nThank you for your purchase!`);

                // Clear cart and redirect
                localStorage.removeItem('cart');
                window.location.href = '/shop/shop.html';
            });
        },

        onError: function(err) {
            console.error('❌ PayPal Error:', err);
            alert('❌ Payment failed. Please try again or contact support.');
        },

        onCancel: function() {
            console.log('⚠️ Payment cancelled by user');
            alert('Payment was cancelled. Your cart is still saved.');
        }

    }).render('#paypal-button-container').catch(function(err) {
        console.error('❌ Failed to render PayPal button:', err);
        document.getElementById('paypal-button-container').innerHTML =
            '<button class="express-btn paypal">PayPal (Error - Check Console)</button>';
    });
}

// Handle payment method selection
function setupPaymentMethods() {
    const paymentRadios = document.querySelectorAll('input[name="payment"]');
    const cardForm = document.getElementById('card-form');

    paymentRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove selected class from all payment methods
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('is-selected');
            });

            // Add selected class to current payment method
            this.closest('.payment-method').classList.add('is-selected');

            // Show/hide card form
            if (this.value === 'card') {
                cardForm.style.display = 'block';
            } else {
                cardForm.style.display = 'none';
            }
        });
    });

    // Set default selection
    const defaultPayment = document.querySelector('input[name="payment"]:checked');
    if (defaultPayment) {
        defaultPayment.closest('.payment-method').classList.add('is-selected');
        if (defaultPayment.value === 'card' && cardForm) {
            cardForm.style.display = 'block';
        }
    }
}

// Handle credit card payment
function processCreditCardPayment() {
    const formData = new FormData(document.getElementById('checkout-form'));
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Validate card fields
    const cardFields = ['cardNumber', 'expiry', 'cvv', 'cardName'];
    const missingCardFields = cardFields.filter(field => !formData.get(field));

    if (missingCardFields.length > 0) {
        alert('Please fill in all card details: ' + missingCardFields.join(', '));
        return;
    }

    // Simulate payment processing
    const submitBtn = document.querySelector('.continue-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Processing...';
    submitBtn.disabled = true;

    setTimeout(() => {
        const orderData = {
            customer: {
                email: formData.get('email'),
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                address: formData.get('address'),
                apartment: formData.get('apartment'),
                postalCode: formData.get('postalCode'),
                city: formData.get('city'),
                country: formData.get('country'),
                delivery: formData.get('delivery')
            },
            cart: cart,
            paymentMethod: 'credit_card',
            paymentDetails: {
                cardLast4: formData.get('cardNumber').slice(-4),
                cardName: formData.get('cardName')
            },
            orderDate: new Date().toISOString(),
            orderNumber: 'VZ' + Date.now()
        };

        // Save order
        localStorage.setItem('lastOrder', JSON.stringify(orderData));
        localStorage.removeItem('cart');

        // Show success
        const orderSummary = cart.map(item =>
            `${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}`
        ).join('\n');

        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        alert(`🎉 Payment Successful!\n\nOrder Number: ${orderData.orderNumber}\nTotal: CHF ${total.toFixed(2)}\n\nItems:\n${orderSummary}\n\nThank you for your purchase!`);

        // Redirect to shop
        window.location.href = '/shop/shop.html';

    }, 2000);
}