// FINAL JAVASCRIPT FOR checkout.js (All English)
document.addEventListener('DOMContentLoaded', () => {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    if (cart.length === 0 && !window.location.pathname.includes('success')) {
        alert("Your cart is empty. Let's find something for you!");
        window.location.href = '/shop/shop.html';
        return;
    }
    
    displayOrderSummary(cart);
    initializePayPal(cart);
});

function displayOrderSummary(cart) {
    const orderItemsContainer = document.getElementById('order-items');
    const summarySubtotalElement = document.getElementById('summary-subtotal');
    const summaryTotalElement = document.getElementById('summary-total');

    if (!orderItemsContainer) return;
    orderItemsContainer.innerHTML = '';

    let subtotal = 0;
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;
        const itemHTML = `
            <div class="summary-item">
                <div class="summary-item-image">
                    <img src="${item.image}" alt="${item.name}">
                    <span class="summary-item-quantity">${item.quantity}</span>
                </div>
                <div class="summary-item-details">
                    <p class="summary-item-name">${item.name}</p>
                    <p class="summary-item-variant">${item.size} / ${item.color}</p>
                </div>
                <p class="summary-item-price">CHF ${itemTotal.toFixed(2)}</p>
            </div>`;
        orderItemsContainer.innerHTML += itemHTML;
    });

    if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
    if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
}

function initializePayPal(cart) {
    if (typeof paypal === 'undefined') {
        console.error('PayPal SDK not loaded');
        return;
    }

    let subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const items = cart.map(item => ({
        name: item.name,
        unit_amount: { currency_code: 'CHF', value: item.price.toFixed(2) },
        quantity: item.quantity.toString(),
        description: `Size: ${item.size}`
    }));

    paypal.Buttons({
        // Your PayPal button configuration here...
    }).render('#paypal-button-container');
}