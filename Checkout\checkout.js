document.addEventListener('DOMContentLoaded', () => {
    // This script now ONLY displays the order summary from localStorage.
    // All authentication logic has been removed as requested.

    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    const orderItemsContainer = document.getElementById('order-items');
    const summarySubtotalElement = document.getElementById('summary-subtotal');
    const summaryTotalElement = document.getElementById('summary-total');

    function displayOrderSummary() {
        if (!orderItemsContainer) {
            console.error("Error: The '#order-items' container was not found.");
            return;
        }

        orderItemsContainer.innerHTML = '';

        if (cart.length === 0) {
            orderItemsContainer.innerHTML = '<p>Your cart is empty.</p>';
            if(summarySubtotalElement) summarySubtotalElement.textContent = 'CHF 0.00';
            if(summaryTotalElement) summaryTotalElement.textContent = 'CHF 0.00';
            return;
        }

        let subtotal = 0;
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            subtotal += itemTotal;
            const itemHTML = `
                <div class="summary-item">
                    <div class="summary-item-image">
                        <img src="${item.image}" alt="${item.name}">
                        <span class="summary-item-quantity">${item.quantity}</span>
                    </div>
                    <div class="summary-item-details">
                        <p class="summary-item-name">${item.name}</p>
                        ${item.size ? `<p class="summary-item-variant">Size: ${item.size}</p>` : ''}
                    </div>
                    <p class="summary-item-price">CHF ${itemTotal.toFixed(2)}</p>
                </div>`;
            orderItemsContainer.innerHTML += itemHTML;
        });

        if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
        if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
    }

    displayOrderSummary();

    // Initialize PayPal after page loads
    setTimeout(initializePayPal, 1000);
});

function initializePayPal() {
    // Check if PayPal SDK is loaded
    if (typeof paypal === 'undefined') {
        console.error('PayPal SDK not loaded');
        return;
    }

    // Get cart total
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    let total = 0;
    cart.forEach(item => {
        total += item.price * item.quantity;
    });

    // Only show PayPal if cart has items
    if (total <= 0) {
        document.getElementById('paypal-button-container').style.display = 'none';
        return;
    }

    // Render PayPal button
    paypal.Buttons({
        style: {
            layout: 'horizontal',
            color: 'blue',
            shape: 'rect',
            label: 'paypal',
            height: 40
        },

        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    amount: {
                        value: total.toFixed(2),
                        currency_code: 'CHF'
                    },
                    description: 'VIVEZ Store Purchase'
                }]
            });
        },

        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                // Payment successful
                alert('Payment completed successfully!\nTransaction ID: ' + details.id);

                // Clear the cart
                localStorage.removeItem('cart');

                // Redirect to shop or success page
                window.location.href = '/shop/shop.html';
            });
        },

        onError: function(err) {
            console.error('PayPal Error:', err);
            alert('Payment failed. Please try again or use a different payment method.');
        },

        onCancel: function() {
            console.log('Payment cancelled by user');
            alert('Payment was cancelled.');
        }

    }).render('#paypal-button-container');
}