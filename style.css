@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400&display=swap');
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}
.footer {
    padding: 4rem 0 0;
    margin: 0;
}
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.brand-title {
    position: absolute;
    top: 20%;
    left: 51.70%;
    transform: translate(-50%, -50%);
    color: white;
    font-family: 'Playfair Display', serif;
    font-size: 7rem !important;
    font-weight: 400;
    letter-spacing: 2.5rem;
    text-transform: uppercase;
    z-index: 10;
    display: block;
    width: 100%;
    text-align: center;
    text-shadow: 0 0 15px rgba(0,0,0,0.2);
    animation: fadeIn 1.5s ease-out;
}
.video-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
    position: relative;
}
.thevideo {
    flex: 1;
    width: 100%;
    height: 100%;
    width: 50%;
    object-fit: cover;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
.thevideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth hover transition */

}
.video-dim {
    transition: all 0.1s ease;
    filter: brightness(0.25) contrast(0.7); /* Reduced from 0.45 to 0.25 for darker effect */
}

.section-text {
    position: absolute;
    top: 65%;
    color: white !important;
    font-family: 'Roboto', sans-serif;
    font-size: 1.5rem !important;
    font-weight: 1000 !important;
    letter-spacing: 0.55rem !important;
    z-index: 10;
    text-shadow: 0 0 20px rgba(0,0,0,0.4);
    opacity: 1;
    pointer-events: none;
    text-transform: uppercase;
    white-space: nowrap;
}
.section-text.left {
    left: 45%;
    transform: translateX(-50%);
}
.section-text.right {
    left: 55%;
    transform: translateX(-50%);
}
.discover-btn {
    position: absolute;
    color: white !important; 
    font-family: sans-serif;
    font-size: 1.2rem;
    text-decoration: underline !important;
    z-index: 10;
    transition: opacity 0.3s ease;
    cursor: pointer;
    top: 72%;  /* Positioned closer to the section text */
    pointer-events: auto;
}
.discover-btn.left {
    left: 45%;
    transform: translateX(-50%);
}
.discover-btn.right {
    left: 55%;
    transform: translateX(-50%);
}
.discover-btn:hover {
    opacity: 0.7;  /* Simple hover effect that won't interfere with videos */
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.video-link {
    display: block;
    width: 100%;
    height: 100%;
}
.left-content, .right-content {
    position: relative;
    flex: 1;
    height: 100%;
    width: 50%;
    min-width: 50%;
}
.footer {
    opacity: 0;
    transition: opacity 0.3s ease;
    background-color: #F9D8E2;
    padding: 1.5rem 2rem;
    margin-top: auto;
    font-family: 'Roboto', sans-serif;
}
.footer.visible {
    opacity: 1;
}
.footer-brand {
    text-align: center;
    margin-bottom: 3rem;
}
.footer-logo {
    height: 80px;
    margin-bottom: 1rem;
}
.footer-slogan {
    color: #333;
    font-style: italic;
    font-size: 1.6rem;
}
.footer-main {
  max-width: 1200px;
  margin: 0 auto;
}
.footer-nav {
  display: flex;
  justify-content: center;
  gap: 5rem;
  margin-bottom: 2.3rem;
}
.footer-link {
  color: #333;
  text-decoration: none;
  font-size: 1.2rem;
  position: relative;
  font-weight: 500;
}
.footer-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -1px;
  left: 0;
  background-color: #333;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.footer-link:hover::after {
  opacity: 1;
}
.footer-policies {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
}
.footer-policies::before {
    content: '';
    position: absolute;
    top: -1rem;
    left: 10%;
    right: 10%;
    height: 1px;
    background-color: rgba(51, 51, 51, 0.2);
}
.footer-policies a {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    transition: text-decoration 0.3s ease;
}
.footer-policies a:hover {
    text-decoration: underline;
}
.footer-copyright {
    text-align: center;
}
.social-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    transition: transform 0.15s ease;
}
.social-icon {
    width: 20px;
    height: 20px;
    color: #333;
}
.social-link:hover .social-icon-wrapper {
    transform: scale(1.2);
}
.social-list {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    list-style: none;
    margin: 2rem 0;
}

.next-page-transition {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    transform: translateX(100%);
    z-index: 9999;
    border: none;
    background-color: white;
    transition: opacity 0.2s;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.slide-in {
    animation: slideFromRight 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards,
               fadeIn 0.4s ease-out forwards;
}

@keyframes slideFromRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0.95;
    }
    to {
        opacity: 1;
    }
}
/* Media Queries */
@media screen and (min-width: 1440px) {
    .brand-title {
        font-size: 7rem;
        letter-spacing: 2.5rem;
    }
    .section-text.left { left: 45%; }
    .section-text.right { left: 55%; }
    .discover-btn.left { left: 45%; }
    .discover-btn.right { left: 55%; }
}

@media screen and (max-width: 1439px) {
    .brand-title {
        font-size: 6rem;
        letter-spacing: 2rem;
        left: 51.70%;
        top: 20%;
    }
    .section-text {
        font-size: 1.6rem !important;
        white-space: nowrap;
        letter-spacing: 0.5rem !important;
    }
    .section-text.left { left: 45%; }
    .section-text.right { left: 55%; }
    .discover-btn {
        font-size: 1.2rem;
    }
    .discover-btn.left { left: 45%; }
    .discover-btn.right { left: 55%; }
}

@media screen and (max-width: 1200px) {
    .brand-title {
        font-size: 5rem !important;
        letter-spacing: 1.8rem;
    }
    .footer-link {
        font-size: 1.2rem;
    }
    .section-text {
        font-size: 1.5rem !important;
        letter-spacing: 0.4rem !important;
    }
}

@media screen and (max-width: 1023px) {
    .brand-title {
        font-size: 4rem !important;
        letter-spacing: 1.5rem;
    }
    .section-text {
        font-size: 1.2rem !important;
        letter-spacing: 0.4rem !important;
    }
    .discover-btn {
        font-size: 1rem;
    }
    .section-text.left { left: 46%; }
}

@media screen and (max-width: 900px) {
    .brand-title {
        font-size: 3.5rem!important;
        letter-spacing: 1.2rem;
    }
    .section-text {
        font-size: 1rem !important;
        letter-spacing: 0.3rem !important;
    }
    .discover-btn {
        font-size: 0.8rem;
    }
    .section-text.left {
        left: 47%;
        white-space: nowrap;
    }
    .footer-link {
        font-size: 1.1rem;
    }
}


@media screen and (max-width: 768px) {
    .brand-title {
        font-size: 3.5rem!important;
        letter-spacing: 1rem;
    }
    .section-text {
        font-size: 0.9rem !important;
        letter-spacing: 0.25rem !important;
    }
    .discover-btn {
        font-size: 0.8rem;
    }
    .section-text.left {
        left: 48%;
        white-space: nowrap;
    }
    .footer-nav {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }
    
    .footer-link {
        font-size: 1rem;
    }
}

@media screen and (max-width: 576px) {
    .brand-title {
        font-size: 3.5rem !important;
        letter-spacing: 1rem;
    }
    .footer-link {
        font-size: 0.9rem;
    }
    .section-text {
        font-size: 0.7rem !important;
        letter-spacing: 0.2rem !important;
    }
}

@media screen and (max-width: 480px) {
    .brand-title {
        font-size: 3rem!important;
        letter-spacing: 0.8rem;
    }
    .section-text {
        font-size: 0.6rem !important;
        letter-spacing: 0.2rem !important;
    }
    .discover-btn {
        font-size: 0.7rem;
    }
    .section-text.left {
        left: 50%;
        white-space: nowrap;
    }
}

/* iPhone-specific fixes */
@media screen and (max-width: 390px) {
    .brand-title {
        font-size: 2rem!important;
        letter-spacing: 0.6rem;
    }
    .section-text {
        font-size: 0.5rem !important;
        letter-spacing: 0.15rem !important;
    }
    .discover-btn {
        font-size: 0.6rem;
    }
}
